// File: src/player-events/playerStart.js
// Example event: when a track starts playing

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'playerStart', // Event name from discord-player
    async execute(queue, track) {
        // queue.metadata contains the channel and client if you set it up in play.js
        if (queue.metadata && queue.metadata.channel) {
            // Enhanced embed with better artwork and information
            const embed = new EmbedBuilder()
                .setColor(0x1DB954) // Spotify green for music
                .setTitle('🎵 Now Playing')
                .setDescription(`**[${track.title}](${track.url})**`)
                .addFields(
                    { name: '🎤 Artist', value: track.author || 'Unknown Artist', inline: true },
                    { name: '⏱️ Duration', value: track.duration || 'Unknown', inline: true },
                    { name: '🎬 Source', value: track.source || 'Unknown', inline: true },
                    { name: '👤 Requested by', value: `${track.requestedBy || 'Unknown'}`, inline: true },
                    { name: '📊 Queue Position', value: `${queue.tracks.size + 1} track${queue.tracks.size !== 0 ? 's' : ''} in queue`, inline: true },
                    { name: '🔊 Volume', value: `${queue.node.volume}%`, inline: true }
                )
                .setTimestamp();

            // Enhanced thumbnail handling with multiple fallbacks
            let thumbnailUrl = null;

            // Try different thumbnail sources in order of preference
            if (track.thumbnail && track.thumbnail.includes('http')) {
                thumbnailUrl = track.thumbnail;
            } else if (track.raw && track.raw.thumbnail) {
                thumbnailUrl = track.raw.thumbnail;
            } else if (track.raw && track.raw.image) {
                thumbnailUrl = track.raw.image;
            } else if (track.metadata && track.metadata.thumbnail) {
                thumbnailUrl = track.metadata.thumbnail;
            }

            // Set the thumbnail or use bot avatar as fallback
            if (thumbnailUrl) {
                embed.setThumbnail(thumbnailUrl);
            } else {
                embed.setThumbnail(queue.metadata.client.user.displayAvatarURL({ size: 512 }));
            }

            try {
                 await queue.metadata.channel.send({ embeds: [embed] });
            } catch (error) {
                console.error("Error sending 'Now Playing' message:", error);
            }
        }
        console.log(`[Player] Started playing: ${track.title} in guild ${queue.guild.name}`);
    }
};